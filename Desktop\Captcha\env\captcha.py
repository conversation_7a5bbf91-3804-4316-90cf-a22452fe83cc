import os
import time
import base64
import io
from dotenv import load_dotenv
from PIL import Image
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI
import requests

# Load environment variables
load_dotenv()

def initialize_llm():
    """Initialize the Google Generative AI model"""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("GOOGLE_API_KEY not found in environment variables. Please set it in your .env file.")

    return ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=api_key,
        temperature=0
    )

def image_to_base64(image_path):
    """Convert image to base64 string"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_captcha_text_with_llm(image_path, llm):
    """Extract text from captcha image using Google's Generative AI"""
    # Convert image to base64
    base64_image = image_to_base64(image_path)

    # Create messages for the LLM
    system_message = SystemMessage(content="""
    You are an expert at reading CAPTCHA images. Your task is to extract the exact text from the CAPTCHA image.

    Rules:
    1. Return ONLY the text you see in the image, nothing else
    2. Do not include any explanations or additional text
    3. If the text contains both letters and numbers, include both
    4. Maintain the exact case (uppercase/lowercase) as shown
    5. If you're unsure about a character, make your best guess
    6. The response should be just the CAPTCHA text
    """)

    human_message = HumanMessage(content=[
        {
            "type": "text",
            "text": "Please extract the text from this CAPTCHA image:"
        },
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{base64_image}"
            }
        }
    ])

    # Get response from LLM
    response = llm.invoke([system_message, human_message])
    return response.content.strip()

# Initialize the LLM
print("Initializing Google Generative AI...")
llm = initialize_llm()
print("✅ LLM initialized successfully!")

# Setup webdriver with undetected chrome
print("Setting up Chrome driver...")
driver = uc.Chrome()
driver.get("https://egramswaraj.gov.in/combinedProfileReport.do")  # Replace with your actual site
print("✅ Navigated to website!")

# Wait for the image to load
print("Waiting for page to load...")
time.sleep(3)

# Locate CAPTCHA image element
print("Looking for CAPTCHA image...")
captcha_img = driver.find_element(By.ID, "img_Capatcha")
captcha_src = captcha_img.get_attribute("src")
print(f"✅ Found CAPTCHA image: {captcha_src[:50]}...")

# Download the CAPTCHA image
print("Downloading CAPTCHA image...")
response = requests.get(captcha_src)
with open("captcha.png", "wb") as f:
    f.write(response.content)
print("✅ CAPTCHA image saved as 'captcha.png'")

# Optional: Preprocess the image (you can keep this or remove it)
print("Processing image...")
image = Image.open("captcha.png")
# Save the processed image (you can add preprocessing here if needed)
image.save("captcha_processed.png")
print("✅ Image processed and saved as 'captcha_processed.png'")

# Extract CAPTCHA text using Google's Generative AI
print("Extracting text using Google AI...")
try:
    captcha_text = extract_captcha_text_with_llm("captcha_processed.png", llm)
    print(f"🎯 Detected CAPTCHA text: '{captcha_text}'")

    # You can use the captcha_text variable for further processing
    # For example, entering it into a form field

except Exception as e:
    print(f"❌ Error extracting CAPTCHA text: {e}")
    captcha_text = ""

# Optional: Clean up temporary files
# os.remove("captcha.png")
# os.remove("captcha_processed.png")

# Close the driver when done (uncomment when ready)
# driver.quit()
print("✅ CAPTCHA processing completed!")