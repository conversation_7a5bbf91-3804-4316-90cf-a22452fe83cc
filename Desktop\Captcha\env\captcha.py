import time
import requests
from PIL import Image
import pytesseract
from selenium import webdriver
from selenium.webdriver.common.by import By

# Setup webdriver
driver = webdriver.Chrome()
driver.get("https://egramswaraj.gov.in/combinedProfileReport.do")  # Replace with your actual site

# Wait for the image to load
time.sleep(2)

# Locate CAPTCHA image element
captcha_img = driver.find_element(By.ID, "img_Capatcha")
captcha_src = captcha_img.get_attribute("src")

# Download the CAPTCHA image
response = requests.get(captcha_src)
with open("captcha.png", "wb") as f:
    f.write(response.content)

# Preprocess the image (optional for better accuracy)
image = Image.open("captcha.png").convert("L")  # grayscale
image = image.point(lambda x: 0 if x < 140 else 255)  # thresholding

# OCR to extract CAPTCHA text
captcha_text = pytesseract.image_to_string(image, config='--psm 8').strip()
print("Detected CAPTCHA:", captcha_text)