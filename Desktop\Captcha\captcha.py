import os
import time
import base64
import io
from dotenv import load_dotenv
from PIL import Image
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import ChatG<PERSON>gleGenerativeAI
 
# 1️⃣ Load API Key and initialize Gemini Vision LLM
load_dotenv()
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    google_api_key=os.getenv("GOOGLE_API_KEY"),
    temperature=0
)
 
# 2️⃣ Launch browser and navigate
driver = uc.Chrome(options=uc.ChromeOptions().add_argument("--start-maximized"))
driver.get("https://egramswaraj.gov.in/combinedProfileReport.do")
time.sleep(2)
 
# 3️⃣ Find and capture only the CAPTCHA element
print("Looking for CAPTCHA element...")
captcha_element = driver.find_element(By.ID, "img_Capatcha")
print("✅ Found CAPTCHA element")

# Take screenshot of only the CAPTCHA element
print("Taking CAPTCHA screenshot...")
captcha_png = captcha_element.screenshot_as_png
with open("captcha.png", "wb") as f:
    f.write(captcha_png)
print("✅ CAPTCHA image saved as 'captcha.png'")

# 4️⃣ Encode CAPTCHA image to base64 for inline prompt
img_b64 = base64.b64encode(captcha_png).decode("utf-8")
image_message = {
    "type": "image",
    "source_type": "base64",
    "mime_type": "image/png",
    "data": img_b64
}
 
# 5️⃣ Prompt Gemini to extract CAPTCHA
messages = [
    SystemMessage(content="You are a helpful assistant that reads CAPTCHA text from images."),
    HumanMessage(content=[
        {"type": "text", "text": "Here is the CAPTCHA image:"},
        image_message,
        {"type": "text", "text": "Please return only the CAPTCHA text visible in the image. do not put space in between characters of captcha text"}
    ])
]
resp = llm.invoke(messages)
captcha_text = resp.content.strip()
print("🔍 Detected CAPTCHA text:", captcha_text)