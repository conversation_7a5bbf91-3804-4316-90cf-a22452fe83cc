import os
import time
import base64
import io
from dotenv import load_dotenv
from PIL import Image
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI
 
# 1️⃣ Load API Key and initialize Gemini Vision LLM
load_dotenv()
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    google_api_key=os.getenv("GOOGLE_API_KEY"),
    temperature=0
)
 
# 2️⃣ Launch browser and navigate
driver = uc.Chrome(options=uc.ChromeOptions().add_argument("--start-maximized"))
driver.get("https://egramswaraj.gov.in/combinedProfileReport.do")
time.sleep(2)
 
# 3️⃣ Take full-page screenshot
full_png = driver.get_screenshot_as_png()
with open("full_screenshot.png", "wb") as f:
    f.write(full_png)
 
# 4️⃣ Encode screenshot to base64 for inline prompt
img_b64 = base64.b64encode(full_png).decode("utf-8")
image_message = {
    "type": "image",
    "source_type": "base64",
    "mime_type": "image/png",
    "data": img_b64
}
 
# 5️⃣ Prompt Gemini to extract CAPTCHA
messages = [
    SystemMessage(content="You are a helpful assistant that finds and reads the CAPTCHA text in the provided screenshot."),
    HumanMessage(content=[
        {"type": "text", "text": "Here is the full page screenshot:"},
        image_message,
        {"type": "text", "text": "Please return only the CAPTCHA text visible on the page. do not put space in between characters of captcha text"}
    ])
]
resp = llm.invoke(messages)
captcha_text = resp.content.strip()
print("🔍 Detected CAPTCHA text:", captcha_text)