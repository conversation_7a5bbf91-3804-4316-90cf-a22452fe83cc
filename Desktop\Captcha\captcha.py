import os
import time
import base64
import io
import requests
from dotenv import load_dotenv
from PIL import Image
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables
load_dotenv()

# Initialize Google Generative AI
def initialize_llm():
    """Initialize the Google Generative AI model"""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("GOOGLE_API_KEY not found in environment variables. Please set it in your .env file.")

    return ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=api_key,
        temperature=0
    )

def image_to_base64(image_path):
    """Convert image to base64 string"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_captcha_text_with_llm(image_path, llm):
    """Extract text from captcha image using Google's Generative AI"""
    # Convert image to base64
    base64_image = image_to_base64(image_path)

    # Create messages for the LLM
    system_message = SystemMessage(content="""
    You are an expert at reading CAPTCHA images. Your task is to extract the exact text from the CAPTCHA image.

    Rules:
    1. Return ONLY the text you see in the image, nothing else
    2. Do not include any explanations or additional text
    3. If the text contains both letters and numbers, include both
    4. Maintain the exact case (uppercase/lowercase) as shown
    5. If you're unsure about a character, make your best guess
    6. The response should be just the CAPTCHA text
    """)

    human_message = HumanMessage(content=[
        {
            "type": "text",
            "text": "Please extract the text from this CAPTCHA image:"
        },
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{base64_image}"
            }
        }
    ])

    # Get response from LLM
    response = llm.invoke([system_message, human_message])
    return response.content.strip()

# Setup webdriver with undetected chrome
driver = uc.Chrome()
driver.get("https://egramswaraj.gov.in/combinedProfileReport.do")  # Replace with your actual site

# Initialize the LLM
llm = initialize_llm()

# Wait for the image to load
time.sleep(2)

# Locate CAPTCHA image element
captcha_img = driver.find_element(By.ID, "img_Capatcha")
captcha_src = captcha_img.get_attribute("src")

# Download the CAPTCHA image
response = requests.get(captcha_src)
with open("captcha.png", "wb") as f:
    f.write(response.content)

# Optional: Preprocess the image for better quality (you can adjust or remove this)
image = Image.open("captcha.png")
# Save the processed image
image.save("captcha_processed.png")

# Extract CAPTCHA text using Google's Generative AI
try:
    captcha_text = extract_captcha_text_with_llm("captcha_processed.png", llm)
    print("Detected CAPTCHA:", captcha_text)
except Exception as e:
    print(f"Error extracting CAPTCHA text: {e}")
    captcha_text = ""

# Clean up temporary files (optional)
# os.remove("captcha.png")
# os.remove("captcha_processed.png")

# Close the driver when done
# driver.quit()