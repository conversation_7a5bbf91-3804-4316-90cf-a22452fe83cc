"""
Setup script to install dependencies and test the CAPTCHA solver
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("🔧 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_env_file():
    """Check if .env file exists and has API key"""
    if not os.path.exists(".env"):
        print("❌ .env file not found!")
        print("Please create a .env file with your Google API key.")
        return False
    
    with open(".env", "r") as f:
        content = f.read()
        if "your_google_api_key_here" in content:
            print("❌ Please update your Google API key in the .env file")
            print("Get your API key from: https://makersuite.google.com/app/apikey")
            return False
    
    print("✅ .env file configured!")
    return True

def main():
    print("🚀 Setting up CAPTCHA solver with Google Vision AI...")
    print("=" * 50)
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Check environment file
    if not check_env_file():
        print("\n📝 To get your Google API key:")
        print("1. Go to https://makersuite.google.com/app/apikey")
        print("2. Create a new API key")
        print("3. Replace 'your_google_api_key_here' in the .env file")
        return
    
    print("\n🎉 Setup completed successfully!")
    print("\nYou can now run: python captcha.py")
    print("\nWhat the script does:")
    print("1. ✅ Opens the website with undetected Chrome")
    print("2. ✅ Finds and downloads the CAPTCHA image")
    print("3. ✅ Sends the image to Google's AI for text extraction")
    print("4. ✅ Returns the extracted text")

if __name__ == "__main__":
    main()
