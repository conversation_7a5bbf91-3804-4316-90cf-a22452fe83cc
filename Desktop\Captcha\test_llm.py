"""
Test script to verify Google AI integration works
"""

import os
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>

def test_google_ai():
    """Test Google AI connection"""
    print("🧪 Testing Google AI connection...")
    
    # Load environment variables
    load_dotenv("config.env")

    # Check API key
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key or api_key == "your_google_api_key_here":
        print("❌ Please set your GOOGLE_API_KEY in the config.env file")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return False
    
    try:
        # Initialize LLM
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0
        )
        
        # Test with a simple message
        from langchain_core.messages import HumanMessage
        response = llm.invoke([HumanMessage(content="Hello! Can you respond with just 'AI Working'?")])
        
        print(f"✅ Google AI Response: {response.content}")
        print("✅ Google AI connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Google AI: {e}")
        return False

if __name__ == "__main__":
    test_google_ai()
