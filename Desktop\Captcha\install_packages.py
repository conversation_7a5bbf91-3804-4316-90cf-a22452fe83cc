"""
Simple script to install all required packages
"""

import subprocess
import sys

# List of packages to install
packages = [
    "python-dotenv>=1.0.0",
    "Pillow>=10.0.0", 
    "undetected-chromedriver>=3.5.0",
    "selenium>=4.15.0",
    "requests>=2.31.0",
    "langchain-core>=0.1.0",
    "langchain-google-genai>=1.0.0",
    "google-generativeai>=0.3.0"
]

def install_packages():
    """Install all required packages"""
    print("🔧 Installing packages for CAPTCHA solver...")
    print("=" * 50)
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing {package}: {e}")
            return False
    
    print("\n🎉 All packages installed successfully!")
    print("\nNext steps:")
    print("1. Edit config.env file and add your Google API key")
    print("2. Get API key from: https://makersuite.google.com/app/apikey")
    print("3. Run: python captcha.py")
    
    return True

if __name__ == "__main__":
    install_packages()
